["tests/test_security.py::TestAPIEndpointSecurity::test_contract_creation_sanitization", "tests/test_security.py::TestAPIEndpointSecurity::test_cors_headers", "tests/test_security.py::TestAPIEndpointSecurity::test_rate_limiting_headers", "tests/test_security.py::TestFileUploadSecurity::test_allowed_file_types", "tests/test_security.py::TestFileUploadSecurity::test_blocked_file_types", "tests/test_security.py::TestFileUploadSecurity::test_file_size_limits", "tests/test_security.py::TestFileUploadSecurity::test_path_traversal_prevention", "tests/test_security.py::TestInputSanitization::test_html_preservation_in_allowed_fields", "tests/test_security.py::TestInputSanitization::test_list_sanitization", "tests/test_security.py::TestInputSanitization::test_nested_object_sanitization", "tests/test_security.py::TestInputSanitization::test_sql_injection_prevention", "tests/test_security.py::TestInputSanitization::test_xss_prevention", "tests/test_security.py::TestSecurityConfiguration::test_allowed_mime_types", "tests/test_security.py::TestSecurityConfiguration::test_security_config_constants", "tests/test_security.py::TestSecurityHeaders::test_csp_header_configuration", "tests/test_security.py::TestSecurityHeaders::test_security_headers_present", "tests/test_security.py::TestSecurityHeaders::test_server_header_obfuscation", "tests/test_security.py::TestSecurityIntegration::test_end_to_end_security_flow", "tests/test_security.py::TestSecurityIntegration::test_security_middleware_performance"]