{"tests/test_security.py::TestInputSanitization::test_xss_prevention": true, "tests/test_security.py::TestInputSanitization::test_sql_injection_prevention": true, "tests/test_security.py::TestInputSanitization::test_html_preservation_in_allowed_fields": true, "tests/test_security.py::TestInputSanitization::test_nested_object_sanitization": true, "tests/test_security.py::TestInputSanitization::test_list_sanitization": true, "tests/test_security.py::TestSecurityHeaders::test_security_headers_present": true, "tests/test_security.py::TestSecurityHeaders::test_csp_header_configuration": true, "tests/test_security.py::TestSecurityHeaders::test_server_header_obfuscation": true, "tests/test_security.py::TestFileUploadSecurity::test_allowed_file_types": true, "tests/test_security.py::TestFileUploadSecurity::test_blocked_file_types": true, "tests/test_security.py::TestFileUploadSecurity::test_file_size_limits": true, "tests/test_security.py::TestFileUploadSecurity::test_path_traversal_prevention": true, "tests/test_security.py::TestAPIEndpointSecurity::test_contract_creation_sanitization": true, "tests/test_security.py::TestAPIEndpointSecurity::test_rate_limiting_headers": true, "tests/test_security.py::TestAPIEndpointSecurity::test_cors_headers": true, "tests/test_security.py::TestSecurityConfiguration::test_security_config_constants": true, "tests/test_security.py::TestSecurityConfiguration::test_allowed_mime_types": true, "tests/test_security.py::TestSecurityIntegration::test_end_to_end_security_flow": true, "tests/test_security.py::TestSecurityIntegration::test_security_middleware_performance": true}